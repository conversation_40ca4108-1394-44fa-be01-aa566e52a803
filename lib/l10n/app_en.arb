{"@@locale": "en", "followerSystemLanguage": "Follow System Language", "simplifiedChinese": "Simplified Chinese", "traditionalChinese": "Traditional Chinese", "english": "English", "japanese": "Japanese", "globalOptSuccess": "Operation Successful", "globalConfirm": "Confirm", "globalCancel": "Cancel", "globalSetting": "Go to Settings", "globalOk": "OK", "globalRecommend": "Recommended", "globalSubmit": "Submit", "globalSave": "Save", "globalClose": "Close", "globalDelete": "Delete", "globalDeleteConfirm": "Confirm Deletion?", "globalDeleteSuccess": "Successfully Deleted", "globalReadImageFail": "Failed to Load Image", "globalClear": "Clear", "globalSuccessful": "Success", "appName": "<PERSON><PERSON><PERSON>", "refresh": "Refresh", "emptyDataMessage": "No Data Available", "listScrollHasMore": "Load More...", "listScrollNoMore": "End of Content", "listLoadFailed": "Failed to Load Data", "listScrollRefreshSuccess": "Refresh Successful", "noNetwork": "Network Error", "accountPasswordError": "Login Failed. Check Password", "signupPhonePlaceholder": "Phone Number", "signupPhoneErrorRequired": "Valid Phone Required", "signupPhoneErrorFormat": "Invalid Phone Format", "signupEmailPlaceholder": "Email", "signupEmailErrorRequired": "<PERSON><PERSON> Required", "signupEmailErrorFormat": "Invalid Email Format", "signupPasswordPlaceholder": "Password", "signupPasswordErrorRequired": "Password Required", "signupPasswordLengthError": "Min {length} characters", "signupConfirmPasswordPlaceholder": "Confirm Password", "signupConfirmPasswordErrorRequired": "Re-enter Password", "signupConfirmPasswordErrorMatch": "Passwords Mismatch", "signupInvitationPlaceholder": "Invitation Code (Optional)", "signupAgreementFrag1": "I agree to", "signupAgreementFrag2": "and", "signupAgreementService": "《Terms of Service》", "signupAgreementPolicy": "《Privacy Policy》", "signupAgreementServiceTitle": "Terms of Service", "signupAgreementPolicyTitle": "Privacy Policy", "signupSignupButton": "Sign Up", "signupSwitchLoginDesc": "Existing Account?", "signupSwitchLoginBtn": "<PERSON><PERSON>", "signupGoogleSignupButton": "Continue with Google", "signupGoogleLoginButton": "Google", "signupVerifyCodeTitle": "Verify Your Email", "signupVerifyCodeDesc": "Verification code sent to {account} (Valid for 15 minutes)", "signupEmailExists": "Email Already Registered", "signupFindPasswordSuccessTitle": "<PERSON><PERSON>", "signupFindPasswordSuccessFg1": "Password reset email sent to", "signupFindPasswordSuccessFg2": "Please check your inbox", "signupResetPasswordSuccessMsg": "Password Reset Successful", "signupVerifyCodeHint": "Verification Code", "sendCodeFailedDesc": "Failed to Send Code", "signupFailedDesc": "Registration Failed. Check Password", "secondsRemaining": "{second}s", "humanVerifyErrorRequired": "Complete CAPTCHA Verification", "findPasswordMainButton": "Reset Password", "findPasswordLoginDesc": "Existing Account?", "findPasswordLoginSignin": "<PERSON><PERSON>", "resetPasswordNoCode": "Verification Code Not Found", "resetPasswordResetButton": "Reset Password", "resetPasswordSuccess": "Password Reset Success", "resetPasswordFailed": "Password Reset Failed", "googleLoginButtonHr": "Or", "loginLoginButton": "<PERSON><PERSON>", "loginForgot": "Forgot Password?", "signupByEmail": "<PERSON>ail Signup", "loginByEmail": "<PERSON><PERSON>", "loginByPhone": "Phone Login", "loginTitle": "<PERSON><PERSON>", "loginOr": "Or", "register_new_account": "Create Account", "loginSubTitle": "Enter Phone or Email", "tabbarConversations": "Chats", "tabbarContacts": "Contacts", "tabbarMine": "Me", "chatMessageUnknown": "Unknown Message Type. Update App.", "dateTimeYesterday": "Yesterday", "systemNotifyUser": "System Notifications", "fileHelperUser": "File Transfer", "functionTitle": "Features", "functionFileHelperTips": "Login on desktop to transfer files between devices", "functionSystemTeamTips": "Official Team Account", "sendMessage": "Send Message", "contactRemark": "Set Remark", "contactRemarkSuccess": "Updated Successfully", "contactSource": "Source", "contactSourceUnknown": "Unknown", "scan": "<PERSON><PERSON>", "addFriend": "Add Friend", "webviewOpenInBrowser": "Open in Browser", "webviewRefresh": "Refresh", "webviewCleanBrowsingData": "<PERSON>ache", "applyFriend": "Add Friend Request", "inputRemark": "Enter Remark", "applyed": "Request Sent", "personalInfo": "Profile", "avatar": "Avatar", "name": "Name", "shortNo": "{appName} ID", "myQrcode": "My QR Code", "inviteCode": "Invite Code", "mobile": "Phone Number", "gender": "Gender", "male": "Male", "female": "Female", "qrDesc": "Scan QR code to add me on {appName}", "search": "Search", "myAppId": "My {appName} ID: {shortNo}", "scanUserQr": "Scan QR Code", "phone": "Phone", "web": "Web", "pc": "PC", "online": "Online", "offline": "Offline", "lastSeenTime": "Last Seen", "justNow": "Just Now", "minAgo": "{min} mins ago", "userRevokeMsg": "\"{user}\" recalled a message", "managerRevokeUserMsg": "You recalled {user}'s message", "managerRevokeUserMsg1": "\"{user}\" recalled a message", "myRevokeMsg": "You recalled a message", "you": "You", "newsLine": "New Messages Below", "lastMsgChatRecord": "[Chat History]", "chatRecord": "Chat History", "chatTitleRecords": "{user}'s Chat <PERSON>", "groupChat": "Group Chat", "userCard": "Profile Card", "chooseCountry": "Select Country/Region", "sendCode": "Send Code", "image": "Image", "card": "Card", "alertDialogTitle": "Notice", "imagePick": "<PERSON>ose Photo", "imageTakePhoto": "Take Photo", "imageGallery": "Album", "deleteFriends": "Remove <PERSON>", "pushBlackList": "Block", "pullOutBlackList": "Unblock", "blackListDesc": "Blocked users can't contact you", "deleteFriendsTips": "Delete {name} and chat history", "joinBlackListTips": "Block this user?", "pullOutBlackListTips": "Unblock this user?", "nickname": "Nickname: {name}", "pressTalk": "Hold to Speak", "releaseToCancel": "Release to Cancel", "holdToRecord": "Swipe Up to Cancel", "timeRemaining": "Time Left: {time}", "newFriends": "New Friends", "savedGroups": "Saved Groups", "blackFriends": "Block List", "requestAddFriend": "Add Friend Request", "agreeApply": "Accept", "agreedApply": "Accepted", "msgSetTop": "<PERSON><PERSON>", "msgCancelTop": "Unpin", "openChannelNotice": "Enable Notifications", "closeChannelNotice": "Disable Notifications", "resendMsgTip": "Resend failed message?", "resendBlacklistGroup": "Blocked in group. Resend?", "resendBlacklistUser": "Blocked user. Resend?", "resendNoRelationUser": "Not friends. Resend?", "resendNoRelationGroup": "Not in group. Resend?", "msgSendFail": "Failed to Send", "msgSendFailResend": "Resend", "statusConnecting": "Connecting...", "statusSuccess": "Connected", "statusKicked": "Logged in Elsewhere", "statusNoNetwork": "Network Error", "statusSyncMsg": "Syncing Messages", "statusFail": "Disconnected", "statusSyncCompleted": "Sync Complete", "chooseContact": "Select Contacts", "startGroupChat": "Create Group", "signalDecryptErr": "[Encrypted Message]", "contentFormatErr": "[Invalid Format]", "noRelationRequest": "{name} requires friend verification", "unknowMsgType": "[Unknown Message]", "renameSuccess": "Updated Successfully", "inviteLink": "Invite Link", "categoryOfficial": "Official", "categoryCustomerService": "Support", "categoryVisitor": "Visitor", "categoryAllStaff": "All Members", "categoryDepartment": "Department", "categoryBot": "Bot", "categoryCommunity": "Community", "chatInfo": "Chat Info", "groupOwner": "Owner", "groupManager": "Admin", "showAllMembers": "View All Members", "groupMembers": "Members", "editGroupNotice": "Editable by Owner/Admin Only", "deleteGroupMembers": "Remove Members", "addGroupMembers": "Add Members", "myInGroupName": "My Group Nickname", "updateInGroupName": "Set Group Nickname", "groupName": "Group Name", "groupQr": "Group QR Code", "groupCard": "Group Info", "groupAnnouncement": "Announcement", "deleteGroup": "Delete and Exit", "clearChatMsg": "Clear History", "searchChatMsg": "Search History", "msgRemind": "Mute Notifications", "msgTop": "<PERSON><PERSON><PERSON>", "saveToMaillist": "Save to Contacts", "showRemarkName": "Show Nicknames", "myRemarkNameInGroup": "My Group Nickname", "unsetting": "Not Set", "deleteMessages": "Delete Messages", "deleteChat": "Delete Chat", "deleteConverMsgTips": "Only deletes from your device", "clearHistory": "Clear History", "clearHistoryTip": "Clear all messages with {name}?", "exitGroupTips": "Exit group silently?", "groupQrDesc": "Valid for {day} days ({date})", "noSavedGroup": "Save groups via \"Save to Contacts\"", "remark": "Remark", "remarkDesc": "Private group remarks", "enterGroupName": "Enter Name", "invitationSent": "<PERSON><PERSON><PERSON>", "lastMsgDraft": "[Draft]", "lastMsgRemind": "[Mention]", "lastMsgCard": "[Contact Card]", "applyJoinGroup": "[Join Request]", "clientWeb": "Web Version", "newMessageNotification": "New Message Alerts", "universal": "General", "takePhoto": "Take Photo", "selectFromPhotoAlbum": "Choose from Album", "saveToPhotoAlbum": "Save to Photos", "clearCacheMemory": "Clear Media Cache", "mulitLanguage": "Languages", "versionInfo": "Version Info", "userAgreement": "Terms of Service", "privacyPolicy": "Privacy Policy", "logOut": "Log Out", "webUrl": "Web Address", "webSide": "Web", "appWeb": "{appName} Web", "visitWebTip": "Visit {appName} in browser and scan QR code. Recommended: Chrome/Firefox", "qrScanLogin": "QR Code Login", "copySuccess": "Copied to Clipboard", "mentionAll": "Everyone", "replyMsgIsRevoked": "Original message recalled", "msgRevoked": "Message was recalled", "copy": "Copy", "delete": "Delete", "revoke": "Recall", "forward": "Forward", "reply": "Reply", "recognize": "Convert to text", "multipleChoice": "Multi-Select", "copied": "<PERSON>pied", "pushText": "Text", "msgImage": "[Image]", "msgVoice": "[Voice]", "msgVideo": "[Video]", "msgLocation": "[Location]", "msgCard": "[Contact]", "msgFile": "[File]", "msgGif": "[GIF]", "msgSticker": "[Sticker]", "msgUnknown": "[Unknown]", "searchMoreContacts": "More Contacts", "searchMoreGroups": "More Groups", "searchMoreMessage": "More Messages", "searchMessageCount": "{count} related messages", "searchMessage": "Message History", "searchRecent": "Recent Chats", "searchGroup": "Groups", "searchContact": "Contacts", "globalSearch": "Search", "cannotReplyMsg": "Muted: Can't reply", "cannotEditMsg": "Muted: Can't edit", "welcomeToGleezy": "Welcome to {name}", "chatAnytime": "Gleeful Times, <PERSON><PERSON>", "forwardSuccess": "Forwarded", "itemForward": "Forward Individually", "itemForwardCount": "[Forward {count} messages]", "mergeForward": "<PERSON><PERSON>", "deleteSelectMsgTip": "Only deletes from your device", "maxChooseMsgCount": "Max {count} messages", "onlyCharactersAndNumbers": "Letters/Numbers Only", "moreThanCharacters": "Min {count} characters", "lessThanCharacters": "Max {count} characters", "tapAndAgree": "By signing up, you agree to", "and": "and", "hadAccount": "Existing Account?", "registerGoLogin": "Phone registered. Login?", "modifySuccess": "Update Successful", "modifyFail": "Update Failed", "avatarSaved": "Avatar saved to Photos", "saveFailed": "Save Failed", "avatarEditing": "Edit Avatar", "needAlbumPermission": "Photo Access Required", "openAlbumPermission": "Enable Photos Permission", "noAvatar": "Set Avatar First", "sendTo": "Send to: ", "reEdit": "Re-edit", "report": "Report", "chooseChat": "Select Chat", "securityAndPrivacy": "Security & Privacy", "resetPassword": "Change Password", "personalInfoCollection": "Data Collection List", "destroyAccount": "Delete Account", "destoryFailedDesc": "Account Deletion Failed", "destoryVerifyCodeHint": "Verification Code", "makeSureDestoryAccount": "Confirm Account Deletion?", "destoryAccountTip": "This action is permanent!", "destory": "Delete", "makeSureLogout": "Confirm Log Out", "logoutTip": "Log out of current account?", "loginExpired": "Session Expired. <PERSON><PERSON><PERSON> Required", "destorySuccess": "Account Deleted!", "generalSettings": "General Settings", "needVoicePermission": "Microphone Access Required", "openVoicePermission": "Enable Microphone Access", "settingSound": "Sound", "settingShock": "Vibration", "smsSendTooFrequently": "Messages sent too frequently", "verifyCodeError": "Verify Code Error", "completeUserInfo": "Complete Profile", "bindPhoneNumber": "Bind Phone Number", "videoCall": "Video Call", "audioCall": "Voice Call", "phoneCall": "Phone Call", "audioCallWaiting": "Waiting for Voice Call", "audioCallIncoming": "Incoming Voice Call", "videoCallWaiting": "Waiting for Video Call", "videoCallIncoming": "Incoming Video Call", "videoCallReject": "Reject", "videoCallAccept": "Accept", "networkError": "Network error. Please try again later", "callEnded": "Call ended", "joinCallFailed": "Failed to join call. Please try again later", "hangupCallFailed": "Failed to hang up. Please try again later", "me": "Me", "emptyPhoneNumber": "Phone number is empty", "switchAccount": "Switch account", "file": "file", "downloadFail": "File download failed", "fileSize": "File size: {size}", "download": "download", "openFile": "Open with other applications", "noAppToOpen": "No application can be processed", "accountNotExist": "Account does not exist", "onlyModifyOnce": "Can only be modified once", "shortNoRule": "Must start with a letter, 6-20 characters, no special characters allowed", "signature": "Personal signature", "signatureLimit": "255 characters or fewer", "inviteNoRule": "4 to 8 characters (letters or numbers), can only be modified once", "clearSuccess": "Cleared successfully", "saveSuccess": "Saved successfully", "inputVerifyCode": "Please enter the verification code", "messageDeleted": "Message has been deleted", "shareToConversation": "Send to a friend", "shareSuccess": "Send successfully", "saveFile": "save", "fileSaved": "File saved to {path}", "saveToGallerySuccess": "Saved to album", "groupSource": "Method to enter the group", "inviteGroup": "Invite to the group", "sayHello": "Say hello", "sayHelloTip": "Say hello and start a chat", "sayHelloGroupRemark": "Hello, I am {userName} of group chat \"{groupName}\"", "sayHelloRemark": "Hello, I am {userName}", "sayHelloSuccess": "Message has been sent", "applyGroupFriendTip": "Send a new friend application", "applyGroupFriendRemark": "I am {userName} of group chat \"{groupName}\"", "applyFriendRemark": "I am {user<PERSON><PERSON>}", "applyFriendFailed": "Friend application failed", "applyExpired": "Expired", "messageRecalled": "This message was recalled", "cropImage": "Crop image", "inCall": "In Call", "missedCall": "Missed call", "noValidQrcode": "No valid QR code", "anonymous": "anonymous", "connectFailed": "Connection failed. Retry?", "connectNoNetwork": "Unable to connect to the network. ", "reconnect": "Reconnect", "invitorNotExist": "The invitation code does not exist", "joinGroupMemberCount": "({count} people in total)", "joinGroupSubmit": "Join this group chat", "joinGroupFail": "Failed to join the group chat!", "foundNewVersion": "New version found!", "upgradeNow": "Update now!", "skip": "skip", "downloading": "Downloading...", "forceUpgradeTitle": "Application upgrade", "forceUpgradeInfo": "It is detected that your APP version is too low\nPlease upgrade to the latest version before use", "noUpgrade": "No updates detected", "needCameraPermission": "Camera permission required", "enableCameraPermission": "Please enable camera permission in settings", "enableAudioPermission": "Please enable microphone access in settings", "forbidden": "Mute", "forbiddenCanNotResend": "You cannot resend messages while muted", "systemMsg": "System Message", "anonymousForbidForward": "Forwarding messages is prohibited in anonymous groups", "phoneNumberExists": "Phone number already exists", "bindEmail": "Bind email address", "emailExists": "Email already exists", "callCanceled": "Canceled", "callNoAnswer": "No Answer", "callRejected": "Call Rejected", "callCanceledByPeer": "Call Canceled by <PERSON><PERSON>", "callMissed": "Missed Call", "callReject": "Rejected", "callDuration": "Call Duration", "callFailed": "Call failed, please check network connection", "pinned": "<PERSON>n", "pinnedBy": "Pinned by {name}", "msgUnpinned": "Unpin", "msgCannotPin": "This message cannot be pinned", "caringMode": "CaringMode", "caringModeSummary": "After turning on \"Reservation Mode\", you can choose the following functions:", "caringModeSummary1": "·The text is larger and the buttons are larger;", "fontSize": "Font Size", "fontSizeStandard": "Standard", "turnOn": "turnOn", "turnOff": "turn off", "changeCaringModeTip": "The settings related to the following mode need to be restarted to take effect", "restartApp": "Restart the application", "restartAppTip": "Please click here to open the application again. ", "caringTurnOn": "turned on", "uploadSuccess": "Upload successful", "uploadFailed": "Upload failed", "deleteFailed": "Delete failed", "finish": "complete", "groupAdmin": "Group Admin", "addGroupAdmin": "Add Group Admin", "deleteGroupAdmin": "Delete Group Admin", "globalAdd": "Add", "msgPinConfirm": "Are you sure you want to pin this message?", "msgPinWithPeer": "Also pin for {name}", "msgCannotUnpin": "You cannot unpin this message", "selectedCount": "{count} selected", "confirmCount": "Confirm({count})", "clearHistoryBoth": "Also delete chat history for the other party", "phoneNumberNotInTargetRegion": "Phone number does not belong to the target region", "dragSliderToCompletePuzzle": "Drag the slider to complete the puzzle", "verificationSuccess": "Verification successful", "verificationFailedTryAgain": "Verification failed, please try again", "addSticker": "Add sticker", "addStickerSuccess": "Added successfully", "addStickerFailed": "Failed to add emoticon", "enableAnonymousTip": "Enable anonymous group", "sliderVerify": "Slide to Verify", "globalConfirmDelete": "Confirm Delete", "dateTimeToday": "Today", "shareToSelf": "Send to My<PERSON>", "shareToFriendsOrGroups": "Send to Friends or Groups", "sharedFromExternalApp": "Shared from External App", "allowSearchByPhone": "Allow others to search for me by phone number", "allowSearchByShort": "Allow others to search for me by short number", "groupNickname": "Group nickname: {name}", "containMemberName": "Include: {name}", "groupChannelName": "Group name: {name}"}