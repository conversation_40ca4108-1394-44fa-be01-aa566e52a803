import 'dart:io';
import 'dart:math';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

class CaringModePage extends StatefulWidget {
  const CaringModePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _CaringModePageState();
  }
}

class _CaringModePageState extends State<CaringModePage> {
  double _currentFontSize = 1.0;

  @override
  void initState() {
    super.initState();
    _currentFontSize = CacheHelper.fontSize;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: SafeArea(
            child: Column(
          children: [
            Expanded(child: _buildContent()),
            SizedBox(
                width: 300,
                child: SubmitButton(
                  text: CacheHelper.caringModel
                      ? context.l10n.turnOff
                      : context.l10n.turnOn,
                  onPressed: () async {
                    var result = (await DialogUtils.showConfirmDialog(
                            context, context.l10n.changeCaringModeTip)) ??
                        false;
                    if (result) {
                      CacheHelper.saveCaringModel(!CacheHelper.caringModel);
                      CommonHelper.restartApp();
                    }
                  },
                )),
            const SizedBox(height: 40),
          ],
        )));
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(20),
        width: double.infinity,
        child: Column(
          children: [
            SizedBox(height: 60),
            Image.asset(
              ImagePath.ic_caring_mode,
              height: 60,
            ),
            SizedBox(height: 20),
            Text(
              context.l10n.caringMode,
              style: TextStyles.fontSize18Normal.copyWith(fontSize: 24),
            ),
            SizedBox(height: 20),
            Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  context.l10n.caringModeSummary,
                  style: TextStyles.fontSize18Normal,
                )),
            SizedBox(height: 20),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                context.l10n.caringModeSummary1,
                style: TextStyles.fontSize16Normal.copyWith(color: Colors.grey),
              ),
            )
          ],
        ),
      ),
    );
  }
}
