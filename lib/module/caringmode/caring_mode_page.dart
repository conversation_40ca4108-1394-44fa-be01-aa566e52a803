import 'dart:io';
import 'dart:math';
import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/common_keys.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/webview/in_app_browser.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/dialog_utils.dart';
import 'package:and/utils/fileUtils.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/http_utils.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/notification_utils.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

class CaringModePage extends StatefulWidget {
  const CaringModePage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _CaringModePageState();
  }
}

class _CaringModePageState extends State<CaringModePage> {
  double _currentFontSize = 1.0;

  @override
  void initState() {
    super.initState();
    _currentFontSize = CacheHelper.fontSize;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: SafeArea(
            child: Column(
          children: [
            Expanded(child: _buildContent()),
            SizedBox(
                width: 300,
                child: SubmitButton(
                  text: CacheHelper.caringModel
                      ? context.l10n.turnOff
                      : context.l10n.turnOn,
                  onPressed: () async {
                    var result = (await DialogUtils.showConfirmDialog(
                            context, context.l10n.changeCaringModeTip)) ??
                        false;
                    if (result) {
                      CacheHelper.saveCaringModel(!CacheHelper.caringModel);
                      CommonHelper.restartApp();
                    }
                  },
                )),
            const SizedBox(height: 40),
          ],
        )));
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.all(20),
        width: double.infinity,
        child: Column(
          children: [
            SizedBox(height: 60),
            Image.asset(
              ImagePath.ic_caring_mode,
              height: 60,
            ),
            SizedBox(height: 20),
            Text(
              context.l10n.caringMode,
              style: TextStyles.fontSize18Normal.copyWith(fontSize: 24),
            ),
            SizedBox(height: 20),
            Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  context.l10n.caringModeSummary,
                  style: TextStyles.fontSize18Normal,
                )),
            SizedBox(height: 20),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                context.l10n.caringModeSummary1,
                style: TextStyles.fontSize16Normal.copyWith(color: Colors.grey),
              ),
            ),
            SizedBox(height: 40),
            _buildFontSizeSlider()
          ],
        ),
      ),
    );
  }

  Widget _buildFontSizeSlider() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.l10n.fontSize,
            style: TextStyles.fontSize16Normal.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.blue,
              inactiveTrackColor: Colors.grey[300],
              thumbColor: Colors.blue,
              overlayColor: Colors.blue.withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
              trackHeight: 4,
            ),
            child: Slider(
              value: _currentFontSize,
              min: 0.8,
              max: 1.4,
              divisions: 6,
              onChanged: (value) {
                setState(() {
                  _currentFontSize = value;
                });
              },
            ),
          ),
          Row(
            children: [
              Text(
                'A',
                style: TextStyles.fontSize14Normal.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: Colors.blue,
                    inactiveTrackColor: Colors.grey[300],
                    thumbColor: Colors.blue,
                    overlayColor: Colors.blue.withOpacity(0.2),
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 12),
                    trackHeight: 4,
                  ),
                  child: Slider(
                    value: _currentFontSize,
                    min: 0.8,
                    max: 1.4,
                    divisions: 6,
                    onChanged: (value) {
                      setState(() {
                        _currentFontSize = value;
                      });
                    },
                  ),
                ),
              ),
              Text(
                'A',
                style: TextStyles.fontSize18Normal.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Center(
            child: Text(
              context.l10n.fontSizeStandard,
              style: TextStyles.fontSize14Normal.copyWith(
                color: Colors.grey[600]
              ),
            ),
          ),

        ],
      ),
    );
  }
}
